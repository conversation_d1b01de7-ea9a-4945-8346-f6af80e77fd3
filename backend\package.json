{"name": "component-generator-backend", "version": "1.0.0", "description": "Backend API for Component Generator Platform", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "axios": "^1.6.2", "joi": "^17.11.0", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}