# Database Configuration
MONGODB_URI=mongodb://localhost:27017/component-generator
# For production, use MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/component-generator

# Server Configuration
PORT=5001
NODE_ENV=production

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_EXPIRE=7d

# AI Service Configuration
OPENROUTER_API_KEY=your-openrouter-api-key-here
GEMINI_API_KEY=your-gemini-api-key-here

# CORS Configuration
FRONTEND_URL=http://localhost:3000
# For production:
# FRONTEND_URL=https://your-domain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
